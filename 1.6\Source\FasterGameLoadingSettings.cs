using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Verse;

namespace FasterGameLoading
{
    public class ParameterInfoContainer : IExposable
    {
        public string Name;
        public string TypeName;
        public bool IsOut;

        public ParameterInfoContainer() { }
        public ParameterInfoContainer(System.Reflection.ParameterInfo pi)
        {
            Name = pi.Name;
            TypeName = pi.ParameterType.AssemblyQualifiedName;
            IsOut = pi.IsOut;
        }

        public void ExposeData()
        {
            Scribe_Values.Look(ref Name, "Name");
            Scribe_Values.Look(ref TypeName, "TypeName");
            Scribe_Values.Look(ref IsOut, "IsOut");
        }
    }

    public class HarmonyArgumentContainer : IExposable
    {
        public string OriginalName;
        public string NewName;

        public HarmonyArgumentContainer() { }
        public HarmonyArgumentContainer(HarmonyLib.HarmonyArgument ha)
        {
            OriginalName = ha.OriginalName;
            NewName = ha.NewName;
        }

        public void ExposeData()
        {
            Scribe_Values.Look(ref OriginalName, "OriginalName");
            Scribe_Values.Look(ref NewName, "NewName");
        }
    }

    public class OriginalParameterContainer : IExposable
    {
        public ParameterInfoContainer parameterInfo;
        public string realName;

        public void ExposeData()
        {
            Scribe_Deep.Look(ref parameterInfo, "parameterInfo");
            Scribe_Values.Look(ref realName, "realName");
        }
    }

    public class ParameterInfoContainerList : IExposable
    {
        public List<ParameterInfoContainer> list = new List<ParameterInfoContainer>();
        public void ExposeData()
        {
            Scribe_Collections.Look(ref list, "list", LookMode.Deep);
        }
    }

    public class OriginalParameterContainerList : IExposable
    {
        public List<OriginalParameterContainer> list = new List<OriginalParameterContainer>();
        public void ExposeData()
        {
            Scribe_Collections.Look(ref list, "list", LookMode.Deep);
        }
    }

    public class HarmonyArgumentContainerList : IExposable
    {
        public List<HarmonyArgumentContainer> list = new List<HarmonyArgumentContainer>();
        public void ExposeData()
        {
            Scribe_Collections.Look(ref list, "list", LookMode.Deep);
        }
    }

    public class CreateReplacementCacheEntry : IExposable
    {
        public string ilBytes;
        public Dictionary<int, string> instructionData = new();

        public void ExposeData()
        {
            Scribe_Values.Look(ref ilBytes, "ilBytes");
            Scribe_Collections.Look(ref instructionData, "instructionData", LookMode.Value, LookMode.Value);
        }
    }

    public class FasterGameLoadingSettings : ModSettings
    {
        public static Dictionary<string, ParameterInfoContainerList> parametersCache = new Dictionary<string, ParameterInfoContainerList>();
        public static Dictionary<string, OriginalParameterContainerList> originalParametersCache = new Dictionary<string, OriginalParameterContainerList>();
        public static Dictionary<string, HarmonyArgumentContainerList> argumentAttributesCache = new Dictionary<string, HarmonyArgumentContainerList>();
        public static Dictionary<string, HarmonyArgumentContainer> parameterArgumentCache = new Dictionary<string, HarmonyArgumentContainer>();
        public static Dictionary<string, CreateReplacementCacheEntry> createReplacementCache = new Dictionary<string, CreateReplacementCacheEntry>();

        public static Dictionary<string, string> loadedTexturesSinceLastSession = new Dictionary<string, string>();
        public static Dictionary<string, ModContentPack> modsByPackageIds = new Dictionary<string, ModContentPack>();
        public static Dictionary<string, string> loadedTypesByFullNameSinceLastSession = new Dictionary<string, string>();
        public static List<string> loadedTypesSinceLastSession = new List<string>();
        public static System.Collections.Concurrent.ConcurrentDictionary<string, byte> typesLoadedThisSession = new System.Collections.Concurrent.ConcurrentDictionary<string, byte>();
        public static List<string> modsInLastSession = new List<string>();
        public static HashSet<string> successfulXMLPathesSinceLastSession = new HashSet<string>();
        public static HashSet<string> failedXMLPathesSinceLastSession = new HashSet<string>();
        public static bool delayGraphicLoading = true;
        public static bool earlyModContentLoading = true;
        public static bool disableStaticAtlasesBaking;
        public static bool xmlCaching = true;
        public static bool xmlInheritanceCaching = true;
        public static bool experimentalOptimizations = true;
        public static bool useV2HarmonyPatches = true;
        public static bool debugMode;
        public Dictionary<string, string> xmlHashes = new Dictionary<string, string>();
        public static ModContentPack GetModContent(string packageId)
        {
            var packageLower = packageId.ToLower();
            if (!modsByPackageIds.TryGetValue(packageLower, out var mod))
            {
                modsByPackageIds[packageLower] = mod = LoadedModManager.RunningModsListForReading.FirstOrDefault(x =>
                    x.PackageIdPlayerFacing.ToLower() == packageLower);
            }
            return mod;
        }

        public static void DoSettingsWindowContents(Rect inRect)
        {
            var ls = new Listing_Standard();
            ls.Begin(new Rect(inRect.x, inRect.y, inRect.width, 500));
            ls.CheckboxLabeled("Load mod content early during game idling periods. When enabled, the game might become not responsive during loading, but it's expected. Disable this if you will encounter any issues.", ref earlyModContentLoading);
            ls.CheckboxLabeled("Prevent graphic and icon loading during startup and load them gradually during playing. Will cut some time off during loading, however it might be not stable and error prone. Disable this if you will encounter any issues.", ref delayGraphicLoading);
            ls.CheckboxLabeled("Disable static atlases backing. Will cut some time off during loading, but might make map rendering perform a bit slower.", ref disableStaticAtlasesBaking);
            ls.CheckboxLabeled("Enable XML caching. This will speed up game loading after the first launch, but may cause issues with mods that dynamically change XML files. Disable if you encounter any issues.", ref xmlCaching);
            ls.CheckboxLabeled("Enable XML inheritance caching. This is a part of XML caching, disable it if you have any issues with inheritance.", ref xmlInheritanceCaching);
            //ls.CheckboxLabeled("Enable experimental optimizations. May break things, use with caution.", ref experimentalOptimizations);
            ls.CheckboxLabeled("Use V2 Harmony Patches. These are faster but might be less stable.", ref useV2HarmonyPatches);
            ls.CheckboxLabeled("Enable debug mode. This is a modder tool, does nothing for end users.", ref debugMode);
            ls.GapLine();
            var explanation = "Some mods may contain a lot of high-res textures that take a long time to load. Use this to downscale hi-res textures. " +
                "Additionally, dds files generated by RimPy will be deleted alongside, so you can perform texture compression by this tool again. " +
                "Following textures will be reduced down to target size: " +
                "\nBuilding - 256px" +
                "\nPawn - 256px" +
                "\nApparel - 128px " +
                "\nWeapon - 128px" +
                "\nItem - 128px" +
                "\nPlant - 128px" +
                "\nTree - 256px" +
                "\nTerrain - 1024px";
            if (ls.ButtonTextLabeled(explanation, "Downscale textures"))
            {
                Find.WindowStack.Add(new Dialog_MessageBox("Perform texture downscaling? It can be reverted by redownloading mods.", "Confirm".Translate(), delegate
                {
                    TextureResize.DoTextureResizing();
                }, "GoBack".Translate()));
            }
            ls.End();
        }
        public override void ExposeData()
        {
            base.ExposeData();
            if (Scribe.mode == LoadSaveMode.Saving)
            {
                loadedTypesSinceLastSession = typesLoadedThisSession.Keys.ToList();
            }
            Scribe_Collections.Look(ref loadedTexturesSinceLastSession, "loadedTexturesSinceLastSession", LookMode.Value, LookMode.Value);
            Scribe_Collections.Look(ref loadedTypesByFullNameSinceLastSession, "loadedTypesByFullNameSinceLastSession", LookMode.Value, LookMode.Value);
            Scribe_Collections.Look(ref loadedTypesSinceLastSession, "loadedTypesSinceLastSession", LookMode.Value);
            Scribe_Collections.Look(ref successfulXMLPathesSinceLastSession, "successfulXMLPathesSinceLastSession", LookMode.Value);
            Scribe_Collections.Look(ref failedXMLPathesSinceLastSession, "failedXMLPathesSinceLastSession", LookMode.Value);
            Scribe_Collections.Look(ref modsInLastSession, "modsInLastSession", LookMode.Value);
            Scribe_Values.Look(ref disableStaticAtlasesBaking, "disableStaticAtlasesBaking");
            Scribe_Values.Look(ref delayGraphicLoading, "delayGraphicLoading", true);
            Scribe_Values.Look(ref earlyModContentLoading, "earlyModContentLoading", true);
            Scribe_Values.Look(ref xmlCaching, "xmlCaching", true);
            Scribe_Values.Look(ref xmlInheritanceCaching, "xmlInheritanceCaching", true);
            Scribe_Values.Look(ref useV2HarmonyPatches, "useV2HarmonyPatches", true);
            Scribe_Values.Look(ref experimentalOptimizations, "experimentalOptimizations", true);
            Scribe_Values.Look(ref debugMode, "debugMode");
            Scribe_Collections.Look(ref xmlHashes, "xmlHashes", LookMode.Value, LookMode.Value);

            Scribe_Collections.Look(ref parametersCache, "parametersCache", LookMode.Value, LookMode.Deep);
            Scribe_Collections.Look(ref originalParametersCache, "originalParametersCache", LookMode.Value, LookMode.Deep);
            Scribe_Collections.Look(ref argumentAttributesCache, "argumentAttributesCache", LookMode.Value, LookMode.Deep);
            Scribe_Collections.Look(ref parameterArgumentCache, "parameterArgumentCache", LookMode.Value, LookMode.Deep);
            Scribe_Collections.Look(ref createReplacementCache, "createReplacementCache", LookMode.Value, LookMode.Deep);

            if (Scribe.mode == LoadSaveMode.PostLoadInit)
            {
                parametersCache ??= new Dictionary<string, ParameterInfoContainerList>();
                originalParametersCache ??= new Dictionary<string, OriginalParameterContainerList>();
                argumentAttributesCache ??= new Dictionary<string, HarmonyArgumentContainerList>();
                parameterArgumentCache ??= new Dictionary<string, HarmonyArgumentContainer>();
                createReplacementCache ??= new Dictionary<string, CreateReplacementCacheEntry>();

                typesLoadedThisSession = new System.Collections.Concurrent.ConcurrentDictionary<string, byte>();
                loadedTexturesSinceLastSession ??= new Dictionary<string, string>();
                loadedTypesByFullNameSinceLastSession ??= new Dictionary<string, string>();
                loadedTypesSinceLastSession ??= new List<string>();
                modsByPackageIds ??= new Dictionary<string, ModContentPack>();
                failedXMLPathesSinceLastSession ??= new HashSet<string>();
                successfulXMLPathesSinceLastSession ??= new HashSet<string>();
                modsInLastSession ??= new List<string>();
                if (!modsInLastSession.SequenceEqual(ModsConfig.ActiveModsInLoadOrder.Select(x => x.packageIdLowerCase)))
                {
                    loadedTexturesSinceLastSession.Clear();
                    loadedTypesByFullNameSinceLastSession.Clear();
                    loadedTypesSinceLastSession.Clear();
                    failedXMLPathesSinceLastSession.Clear();
                    successfulXMLPathesSinceLastSession.Clear();
                    xmlHashes.Clear();

                    parametersCache.Clear();
                    originalParametersCache.Clear();
                    argumentAttributesCache.Clear();
                    parameterArgumentCache.Clear();
                    createReplacementCache.Clear();

                    Utils.Log("Mods changed, clearing cache");
                }
            }
        }
    }
}

