using HarmonyLib;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using Verse;


namespace FasterGameLoading
{
    [HarmonyPatch(typeof(DirectXmlLoader), "XmlAssetsInModFolder")]
    public static class DirectXmlLoader_XmlAssetsInModFolder_Patch
    {
        private static readonly LoadableXmlAsset[] EmptyXmlAssetsArray = Array.Empty<LoadableXmlAsset>();
        public static bool Prefix(ModContentPack mod, string folderPath, List<string> foldersToLoadDebug, ref LoadableXmlAsset[] __result)
        {
            var folders = foldersToLoadDebug ?? mod.foldersToLoadDescendingOrder;
            if (folders.Count == 0)
            {
                __result = EmptyXmlAssetsArray;
                return false;
            }

            var filesByFolder = new ConcurrentDictionary<string, List<FileInfo>>();
            Parallel.ForEach(folders, folder =>
            {
                var path = Path.Combine(folder, folderPath);
                var directory = new DirectoryInfo(path);
                if (directory.Exists)
                {
                    filesByFolder.TryAdd(folder, directory.GetFiles("*.xml", SearchOption.AllDirectories).ToList());
                }
            });

            var xmlFiles = new Dictionary<string, FileInfo>();
            foreach (var folder in folders)
            {
                if (filesByFolder.TryGetValue(folder, out var foundFiles))
                {
                    foreach (var file in foundFiles)
                    {
                        var key = file.FullName.Substring(folder.Length + 1);
                        xmlFiles.TryAdd(key, file);
                    }
                }
            }

            if (xmlFiles.Count == 0)
            {
                __result = EmptyXmlAssetsArray;
                return false;
            }
            
            var files = xmlFiles.Values.OrderBy(f => f.FullName).ToList();
            var assets = new LoadableXmlAsset[files.Count];
            Parallel.For(0, files.Count, i =>
            {
                assets[i] = new LoadableXmlAsset(files[i], mod);
            });
            
            __result = assets;
            return false;
        }
    }
}
