<?xml version='1.0' encoding='utf-8'?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Library</OutputType>
    <TargetFramework>net48</TargetFramework>
    <!-- Ensure this matches the original framework version -->
    <RootNamespace>FasterGameLoading</RootNamespace>
    <AssemblyName>FasterGameLoading</AssemblyName>
    <LangVersion>latest</LangVersion>
    <Deterministic>true</Deterministic>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <!-- Prevent appending target framework to the output path -->
  </PropertyGroup>
  <!-- Debug configuration -->
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <Optimize>true</Optimize>
    <OutputPath>..\Assemblies\</OutputPath>
    <!-- Output directly to Assemblies -->
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>none</DebugType>
    <!-- Set to 'none' to exclude debug info -->
  </PropertyGroup>
  <!-- Release configuration -->
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <Optimize>true</Optimize>
    <OutputPath>..\Assemblies\</OutputPath>
    <!-- Output directly to Assemblies -->
    <DefineConstants>TRACE</DefineConstants>
    <DebugType>none</DebugType>
    <!-- Set to 'none' to exclude debug info -->
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System"/>
    <Reference Include="System.Core"/>
    <Reference Include="System.Xml.Linq"/>
    <Reference Include="System.Data.DataSetExtensions"/>
    <Reference Include="Microsoft.CSharp"/>
    <Reference Include="System.Data"/>
    <Reference Include="System.Net.Http"/>
    <Reference Include="System.Xml"/>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Krafs.Publicizer" Version="2.*" PrivateAssets="all">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Krafs.Rimworld.Ref" Version="1.6.*-*"/>
    <PackageReference Include="Lib.Harmony" Version="2.*">
      <ExcludeAssets>runtime</ExcludeAssets>
      <IncludeAssets>compile; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <PropertyGroup>
    <!-- Disable automatic generation of assembly attributes -->
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <ItemGroup>
    <Publicize Include="Assembly-CSharp;0Harmony"/>
  </ItemGroup>
</Project>
