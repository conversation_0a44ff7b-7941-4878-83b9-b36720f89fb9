using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using HarmonyLib;
using RimWorld;
using RimWorld.IO;
using RuntimeAudioClipLoader;
using UnityEngine;
using Verse;
using Object = UnityEngine.Object;

namespace FasterGameLoading
{
    public class FasterGameLoadingMod : Mod
    {
        public static Harmony harmony;
        public static FasterGameLoadingSettings settings;
        public static LoadingActions loadingActions;
        public FasterGameLoadingMod(ModContentPack pack) : base(pack)
        {
            var gameObject = new GameObject("FasterGameLoadingMod");
            Object.DontDestroyOnLoad(gameObject);
            loadingActions = gameObject.AddComponent<LoadingActions>();
            settings = this.GetSettings<FasterGameLoadingSettings>();
            harmony = new Harmony("FasterGameLoadingMod");
            harmony.PatchAll(Assembly.GetExecutingAssembly());
            PreloadingManager.StartPreloading();
            if (FasterGameLoadingSettings.debugMode)
            {
                //ProfileTypes();
            }
        }

        private void ProfileTypes()
        {
            var typesToProfile = new List<Type>
            {
                typeof(AccessTools),
                typeof(DefInjectionPackage),
                typeof(ParseHelper),
                typeof(StaticConstructorOnStartupUtility),
                typeof(PatchClassProcessor),
                typeof(PatchInfo),
                typeof(FilesystemFile),
                typeof(GlobalTextureAtlasManager),
                typeof(GenTypes),
                typeof(XmlInheritance),
                typeof(LoadedLanguage),
                typeof(LanguageDatabase),
                typeof(DirectXmlCrossRefLoader),
                typeof(GenDefDatabase),
                typeof(PlayDataLoader),
                typeof(ModLister),
                typeof(Mod),
                typeof(DirectXmlLoader),
                typeof(DefInjectionPackage),
                typeof(DefGenerator),
                typeof(PlayerKnowledgeDatabase),
                typeof(KeyPrefs),
                typeof(Prefs),
                typeof(ShortHashGiver),
                typeof(ModContentLoader<AudioClip>),
                typeof(Manager),
                typeof(BackstoryTranslationUtility),
            };
            // --- Core Patching & IL Generation Engine (Highest Impact) ---
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.MethodPatcher"));          // Orchestrates the entire replacement method creation. (7015ms total)
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.MethodBodyReader"));       // Reads and finalizes IL. (FinalizeILCodes: 2384ms, GenerateInstructions: 312ms, ReadOperand: 240ms)
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.MethodCopier"));           // Manages the transpiler chain and calls MethodBodyReader.
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.CodeTranspiler"));         // The class that executes the transpiler chain inside FinalizeILCodes.
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.Emitter"));                // Handles writing the new IL. (Emit: 255ms, AddInstruction: 224ms)

            // --- High-Level Orchestration & Sorting ---
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.PatchClassProcessor"));    // Processes entire patch classes. (934ms)
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.PatchFunctions"));         // Contains the top-level UpdateWrapper method. (184ms)
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.PatchSorter"));            // Sorts patches. (AddNodeToResult: 547ms)

            // --- High-Frequency Reflection & Utility Helpers ---
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.AccessTools"));            // General reflection helper. (TypeByName: 241ms)
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.Traverse"));               // Used for reflection on private members. (Field: 231ms)
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.PatchTools"));             // Utility functions for patching.
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.PatchArgumentExtensions"));// Processes custom argument attributes. (GetArgumentAttributes: 220ms)

            // --- Core Data Structures (Profiling these can reveal allocation hotspots) ---
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.ILInstruction"));          // The internal representation of a single instruction.
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.CodeInstruction"));        // The public representation of an instruction passed to transpilers.
            typesToProfile.Add(AccessTools.TypeByName("HarmonyLib.CodeMatcher"));            // Helper for searching and manipulating instructions.
            typesToProfile.Add(typeof(HarmonyPerformancePatches));
            typesToProfile.AddRange(GenTypes.AllSubclasses(typeof(Def)));
            typesToProfile.AddRange(GenTypes.AllSubclasses(typeof(CompProperties)));
            //typesToProfile.AddRange(typeof(FasterGameLoadingMod).Assembly.GetTypes());
            PerformanceProfiling.harmony = harmony;
            PerformanceProfiling.ProfileTypes(typesToProfile.Distinct().ToHashSet());


        }

        public override string SettingsCategory()
        {
            return this.Content.Name;
        }

        public override void DoSettingsWindowContents(Rect inRect)
        {
            base.DoSettingsWindowContents(inRect);
            FasterGameLoadingSettings.DoSettingsWindowContents(inRect);
        }
    }
}

