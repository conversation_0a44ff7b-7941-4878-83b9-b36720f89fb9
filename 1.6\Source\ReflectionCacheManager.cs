using HarmonyLib;
using System;
using System.Runtime.CompilerServices;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Verse;
using System.Reflection;
using System.Collections.Concurrent;
using System.Text.RegularExpressions;
using System.Text;

namespace FasterGameLoading
{
    public static class ReflectionCacheManager
    {
        public static Task PreloadTask;
        public static readonly ConcurrentDictionary<string, Type> FoundTypes = new();

        public static void RegisterFoundType(Type type, string originalName, string usedName, bool wasCacheHit)
        {
            if (type == null)
            {
                return;
            }
            var watch = Stopwatch.StartNew();
            var fullname = type.FullName;

            if (!wasCacheHit)
            {
                if (originalName != null) FoundTypes[originalName] = type;
                if (usedName != null) FoundTypes[usedName] = type;
                if (fullname != null) FoundTypes[fullname] = type;
            }
            if (fullname != null) FasterGameLoadingSettings.typesLoadedThisSession.TryAdd(fullname, 0);

            if (originalName != null && originalName != fullname)
            {
                FasterGameLoadingSettings.loadedTypesByFullNameSinceLastSession[originalName] = fullname;
            }

            watch.Stop();
            LoadingActions.RegisterFoundTypeTracker.totalTicks += watch.ElapsedTicks;
            LoadingActions.RegisterFoundTypeTracker.callCount++;
        }

        public static bool TryGetFromCache(ref string name, out Type result, out (string originalTypeName, bool wasCacheHit) state)
        {
            var watch = Stopwatch.StartNew();
            try
            {
                Interlocked.Increment(ref LoadingActions.ReflectionTypeCacheProgress.total);
                var originalName = name;
                if (FasterGameLoadingSettings.loadedTypesByFullNameSinceLastSession.TryGetValue(name, out var fullName))
                {
                    name = fullName;
                }

                if (FoundTypes.TryGetValue(name, out result))
                {
                    Interlocked.Increment(ref LoadingActions.ReflectionTypeCacheProgress.processed);
                    state = (originalName, true);
                    return true;
                }
                state = (originalName, false);
                return false;
            }
            finally
            {
                watch.Stop();
                LoadingActions.TryGetFromCacheTracker.totalTicks += watch.ElapsedTicks;
                LoadingActions.TryGetFromCacheTracker.callCount++;
            }
        }

        public static void StartPreloading()
        {
            var tasks = new List<Task>();
            tasks.Add(Task.Run(() =>
            {
                try { AccessTools_AllTypes_Patch.DoCache(); }
                catch (Exception ex) { Log.Error($"[FasterGameLoading] Exception in AccessTools_AllTypes_Patch.DoCache: {ex}"); }
            }));
            tasks.Add(Task.Run(() => DoTypeLookupsCache()));
            tasks.Add(Task.Run(() => PreloadHarmonyReflectionCache()));
            PreloadTask = Task.WhenAll(tasks);
        }

        private static void DoTypeLookupsCache()
        {
            try
            {
                var typeNames = FasterGameLoadingSettings.loadedTypesSinceLastSession.ToList();
                AccessTools_TypeByName_Patch.ignore = true;
                var stopProfiling = PerformanceProfiling.stopProfiling;
                PerformanceProfiling.stopProfiling = true;
                if (typeNames.Count > 0)
                {
                    GenThreading.ParallelFor(0, typeNames.Count, i =>
                    {
                        var typeName = typeNames[i];
                        if (FoundTypes.ContainsKey(typeName) is false)
                        {
                            FoundTypes[typeName] = AccessTools.TypeByName(typeName);
                        }
                    });
                }
                PerformanceProfiling.stopProfiling = stopProfiling;
                AccessTools_TypeByName_Patch.ignore = false;
            }
            catch (Exception ex)
            {
                Log.Error($"[FasterGameLoading] Exception in DoTypeLookupsCache: {ex}");
            }
        }

        private static List<string> SplitParameterTypes(string parameters)
        {
            var result = new List<string>();
            var sb = new StringBuilder();
            int bracketDepth = 0;

            foreach (char c in parameters)
            {
                if (c == '[' || c == '(') bracketDepth++;
                if (c == ']' || c == ')') bracketDepth--;

                if (c == ',' && bracketDepth == 0)
                {
                    result.Add(sb.ToString());
                    sb.Clear();
                }
                else
                {
                    sb.Append(c);
                }
            }
            result.Add(sb.ToString());
            return result;
        }

        private static MethodBase GetMethodFromKey(string key)
        {
            var match = Regex.Match(key, @"^(.*):(.*)\((.*)\)$");
            if (!match.Success) return AccessTools.Method(key);

            var typeName = match.Groups[1].Value;
            var methodName = match.Groups[2].Value;
            var parameters = match.Groups[3].Value;

            var type = AccessTools.TypeByName(typeName);
            if (type == null) return null;

            var paramTypeNames = string.IsNullOrEmpty(parameters) ? [] : SplitParameterTypes(parameters);
            var paramTypes = paramTypeNames.Select(AccessTools.TypeByName).ToArray();

            if (paramTypes.Any(t => t == null)) return null;

            if (methodName == ".ctor")
            {
                return AccessTools.Constructor(type, paramTypes);
            }
            return AccessTools.Method(type, methodName, paramTypes);
        }

        public static void PreloadHarmonyReflectionCache()
        {
            var parametersToPreload = FasterGameLoadingSettings.parametersCache.ToList();
            var originalParametersToPreload = FasterGameLoadingSettings.originalParametersCache.ToList();
            var argumentAttributesToPreload = FasterGameLoadingSettings.argumentAttributesCache.ToList();
            var parameterArgumentToPreload = FasterGameLoadingSettings.parameterArgumentCache.ToList();

            var watch = Stopwatch.StartNew();
            try
            {
                Log.Message("[FasterGameLoading] Starting Harmony reflection cache preloading.");
                LoadingActions.HarmonyReflectionCacheProgress.total = parametersToPreload.Count + originalParametersToPreload.Count + argumentAttributesToPreload.Count + parameterArgumentToPreload.Count;

                foreach (var kvp in parametersToPreload)
                {
                    try
                    {
                        var method = GetMethodFromKey(kvp.Key);
                        if (method != null)
                        {
                            HarmonyPerformancePatches._parametersCache.GetValue(method, m =>
                            {
                                var parameters = kvp.Value.list.Select(c => new LoadedParameterInfo(c)).ToArray();
                                return parameters;
                            });
                        }
                        else
                        {
                            Log.Warning($"[FasterGameLoading] Could not find method for key: {kvp.Key}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"[FasterGameLoading] Exception while preloading parameters cache for key: {kvp.Key}\n{ex}");
                    }
                    LoadingActions.HarmonyReflectionCacheProgress.processed++;
                }

                foreach (var kvp in originalParametersToPreload)
                {
                    try
                    {
                        var method = GetMethodFromKey(kvp.Key) as MethodInfo;
                        if (method != null)
                        {
                            HarmonyPerformancePatches._originalParametersCache.GetValue(method, m =>
                            {
                                var parameters = kvp.Value.list.Select(c => (info: (ParameterInfo)new LoadedParameterInfo(c.parameterInfo), realName: c.realName)).ToList();
                                return parameters;
                            });
                        }
                        else
                        {
                            Log.Warning($"[FasterGameLoading] Could not find method for key: {kvp.Key}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"[FasterGameLoading] Exception while preloading original parameters cache for key: {kvp.Key}\n{ex}");
                    }
                    LoadingActions.HarmonyReflectionCacheProgress.processed++;
                }

                foreach (var kvp in argumentAttributesToPreload)
                {
                    try
                    {
                        var member = (GetMethodFromKey(kvp.Key) as MemberInfo) ?? (AccessTools.Field(kvp.Key) as MemberInfo) ?? AccessTools.Property(kvp.Key);
                        if (member != null)
                        {
                            HarmonyPerformancePatches._argumentAttributesCache.GetValue(member, m =>
                            {
                                var attributes = kvp.Value.list.Select(c => new HarmonyArgument(c.OriginalName, c.NewName)).ToList();
                                return attributes;
                            });
                        }
                        else
                        {
                            Log.Warning($"[FasterGameLoading] Could not find member for key: {kvp.Key}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"[FasterGameLoading] Exception while preloading argument attributes cache for key: {kvp.Key}\n{ex}");
                    }
                    LoadingActions.HarmonyReflectionCacheProgress.processed++;
                }

                foreach (var kvp in parameterArgumentToPreload)
                {
                    try
                    {
                        var keyParts = kvp.Key.Split(new[] { '-' }, 2);
                        var methodKey = keyParts[0];
                        var parameterName = keyParts[1];

                        var method = GetMethodFromKey(methodKey);
                        if (method != null)
                        {
                            var parameter = method.GetParameters().FirstOrDefault(p => p.Name == parameterName);
                            if (parameter != null)
                            {
                                HarmonyPerformancePatches._parameterArgumentCache.GetValue(parameter, p =>
                                {
                                    var attribute = new HarmonyArgument(kvp.Value.OriginalName, kvp.Value.NewName);
                                    return attribute;
                                });
                            }
                            else
                            {
                                Log.Warning($"[FasterGameLoading] Could not find parameter '{parameterName}' for key: {kvp.Key}");
                            }
                        }
                        else
                        {
                            Log.Warning($"[FasterGameLoading] Could not find method for key: {methodKey}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error($"[FasterGameLoading] Exception while preloading parameter argument cache for key: {kvp.Key}\n{ex}");
                    }
                    LoadingActions.HarmonyReflectionCacheProgress.processed++;
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[FasterGameLoading] Unhandled exception in PreloadHarmonyReflectionCache: {ex}");
            }
            finally
            {
                watch.Stop();
                LoadingActions.HarmonyReflectionCacheTracker.totalTicks += watch.ElapsedTicks;
                LoadingActions.HarmonyReflectionCacheTracker.callCount++;
                Log.Message($"[FasterGameLoading] Finished Harmony reflection cache preloading in {watch.ElapsedMilliseconds}ms.");
            }
        }

        public static void PreloadCreateReplacementCache()
        {
            try
            {
                var cacheKeysToPreload = FasterGameLoadingSettings.createReplacementCache.Keys.ToList();
                Log.Message($"[FasterGameLoading] Found {cacheKeysToPreload.Count} CreateReplacement cache entries to preload.");

                foreach (var cacheKey in cacheKeysToPreload)
                {
                    HarmonyPerformancePatches._generatedMethodCache[cacheKey] = null;
                    HarmonyPerformancePatches._finalInstructionsCache[cacheKey] = null;
                }

                if (cacheKeysToPreload.Count > 0)
                {
                    Log.Message($"[FasterGameLoading] Preloaded {cacheKeysToPreload.Count} CreateReplacement cache entries.");
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[FasterGameLoading] Exception in PreloadCreateReplacementCache: {ex}");
            }
        }
    }

    public class LoadedParameterInfo : ParameterInfo
    {
        public LoadedParameterInfo(FasterGameLoading.ParameterInfoContainer container)
        {
            NameImpl = container.Name;
            ClassImpl = Type.GetType(container.TypeName);
            AttrsImpl = container.IsOut ? ParameterAttributes.Out : ParameterAttributes.None;
        }
    }
}
